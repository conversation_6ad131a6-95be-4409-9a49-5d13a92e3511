from fastapi import FastAP<PERSON>, APIRouter
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager
from database import init_db, close_db
from authentication.routes import router as auth_router
from members.routes import router as members_router
from membership_plan.routes import router as membership_plan_router
from attendance.routes import router as attendance_router
from inventory.routes import router as inventory_router
from payments.routes import router as payments_router
from classes.routes import router as classes_router
from accounts.routes import router as accounts_router
from fastapi_pagination import add_pagination
from fastapi_pagination.utils import disable_installed_extensions_check

router = APIRouter()


@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    await init_db()
    yield
    # Shutdown
    await close_db()

app = FastAPI(lifespan=lifespan)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)





    
app.include_router(members_router)
app.include_router(membership_plan_router)
app.include_router(attendance_router)
app.include_router(inventory_router)
app.include_router(payments_router)
app.include_router(classes_router)
app.include_router(auth_router)
app.include_router(accounts_router)

app.include_router(router)
add_pagination(app)
