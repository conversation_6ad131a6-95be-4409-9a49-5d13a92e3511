from pydantic import BaseModel
from typing import Optional
from uuid import UUID
from pydantic import Field
from datetime import datetime


class BaseSchema(BaseModel):
    id: Optional[UUID] = Field(None)
    created_at: Optional[datetime] = Field(None)
    updated_at: Optional[datetime] = Field(None)


class BaseCreateSchema(BaseModel):
    pass

    

class BaseResponse(BaseModel):
    message: str
