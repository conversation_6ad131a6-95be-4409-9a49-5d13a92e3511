from uuid import UUID
from datetime import timedelta
from fastapi import APIRouter, Depends, HTTPException, Query
from fastapi_utils.cbv import cbv
from typing import Optional
from members.models import Member
from members.schemas import MemberCreateSchema, MemberSchema, MemberResponseSchema
from membership_plan.models import MembershipPlan
from authentication.utils import get_current_active_user
from schemas import BaseResponse
from fastapi_pagination import Page, paginate

# router = APIRouter(prefix="/members", tags=["members"], 
#                    dependencies=[Depends(get_current_active_user)])


router = APIRouter(prefix="/payments", tags=["payments"])


@cbv(router)
class MemberCBV:

    @router.post("/", response_model=MemberResponseSchema)
    async def create_member(self, request: MemberCreateSchema):
        try:
            data = request.dict(exclude={'membership_plan'})
            
        
            if request.membership_plan:
                plan = await MembershipPlan.get_or_none(id=request.membership_plan)
                
                if not plan:
                    raise HTTPException(status_code=404, detail="Membership plan not found")
                
                
                data['membership_plan_id'] = request.membership_plan
                if request.membership_start_date:
                    data['membership_end_date'] = request.membership_start_date + timedelta(days=plan.duration_days)
            

        
            member = await Member.create(**data)
            
            member = await Member.get(id=member.id).prefetch_related('membership_plan')
            return member
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e)) from e

    @router.get("/", response_model=Page[MemberResponseSchema])
    async def get_members(
        self,
        is_active: Optional[bool] = Query(None),
        search: Optional[str] = Query(None)
    ):
        try:
            query = Member.all()
            
            if is_active is not None:
                query = query.filter(is_active=is_active)
            if search:
                query = query.filter(name__icontains=search)
            
            members = await query.prefetch_related('membership_plan')
            return paginate(members)
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e)) from e

    @router.get("/{member_id}/", response_model=MemberResponseSchema)
    async def get_member(self, member_id: UUID):
        try:
            member = await Member.get_or_none(id=member_id).prefetch_related('membership_plan')
            if not member:
                raise HTTPException(status_code=404, detail="Member not found")
            return member
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e)) from e

    @router.put("/{member_id}/", response_model=MemberResponseSchema)
    async def update_member(self, member_id: UUID, request: MemberCreateSchema):
        try:
            data = request.dict(exclude_unset=True, exclude={'membership_plan'})
            
            if request.membership_plan:
                plan = await MembershipPlan.get_or_none(id=request.membership_plan)
                if not plan:
                    raise HTTPException(status_code=404, detail="Membership plan not found")
                
                data['membership_plan_id'] = request.membership_plan
                if request.membership_start_date:
                    data['membership_end_date'] = request.membership_start_date + timedelta(days=plan.duration_days)

            updated_count = await Member.filter(id=member_id).update(**data)
            if not updated_count:
                raise HTTPException(status_code=404, detail="Member not found")

            member = await Member.get(id=member_id).prefetch_related('membership_plan')
            return member
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e)) from e

    @router.delete("/{member_id}/", response_model=BaseResponse)
    async def delete_member(self, member_id: UUID):
        try:
            member = await Member.get_or_none(id=member_id)
            if not member:
                raise HTTPException(status_code=404, detail="Member not found")

            await member.delete()
            return BaseResponse(message=f"Member '{member.name}' deleted successfully")
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e)) from e
