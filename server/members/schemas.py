from typing import Optional
from datetime import date
from pydantic import EmailStr
from pydantic import Field
from core.schema import BaseSchema,BaseCreateSchema
from uuid import UUID
from membership_plan.schemas import MembershipPlanSchema


class MemberCreateSchema(BaseCreateSchema):
    name: str
    email: Optional[EmailStr] = None
    phone: Optional[str] = None
    date_of_birth: Optional[date] = None
    gender: Optional[str] = None
    address: Optional[str] = None
    emergency_contact: Optional[str] = None
    membership_plan: Optional[UUID] = None
    membership_start_date: Optional[date] = None
    membership_end_date: Optional[date] = None
    is_active: Optional[bool] = True
    
   

class MemberSchema(MemberCreateSchema,BaseSchema):
 
    
    membership_plan: Optional[MembershipPlanSchema] = None 

    class Config:
        from_attributes = True


class MemberResponseSchema(MemberSchema):
    pass
