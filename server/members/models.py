from core.models import BaseModel,models


class Member(BaseModel):
    name = models.Char<PERSON><PERSON>(max_length=255)
    email = models.Char<PERSON>ield(max_length=255, null=True)
    phone = models.CharField(max_length=20, null=True)
    date_of_birth = models.DateField(null=True)
    gender = models.CharField(max_length=10, null=True)
    address = models.TextField(null=True)
    emergency_contact = models.CharField(max_length=255, null=True)
    membership_plan = models.ForeignKeyField('models.MembershipPlan', null=True)
    membership_start_date = models.DateField(null=True)
    membership_end_date = models.DateField(null=True)
    is_active = models.BooleanField(default=True)
    
    class Meta:
        table = "members"
