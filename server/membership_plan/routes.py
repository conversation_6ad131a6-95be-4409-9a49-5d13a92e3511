from uuid import UUID
from fastapi import APIRouter, Depends, HTTPException
from fastapi_utils.cbv import cbv
from membership_plan.models import MembershipPlan
from membership_plan.schemas import MembershipPlanSchema, MembershipPlanResponse
from authentication.utils import get_current_active_user
from schemas import BaseResponse
from fastapi_pagination import Page, paginate

router = APIRouter(prefix="/membership-plans", tags=["membership-plans"], 
                   dependencies=[Depends(get_current_active_user)])


@cbv(router)
class MembershipPlanCBV:

    @router.post("/", response_model=MembershipPlanResponse)
    async def create_plan(self, request: MembershipPlanSchema):
        try:
            plan = await MembershipPlan.create(**request.dict())
            return plan
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e)) from e

    @router.get("/", response_model=Page[MembershipPlanResponse])
    async def get_plans(self):
        try:
            plans = await MembershipPlan.all()
            return paginate(plans)
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e)) from e

    @router.get("/{plan_id}/", response_model=MembershipPlanResponse)
    async def get_plan(self, plan_id: UUID):
        try:
            plan = await MembershipPlan.get_or_none(id=plan_id)
            if not plan:
                raise HTTPException(status_code=404, detail="Plan not found")
            return plan
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e)) from e

    @router.put("/{plan_id}/", response_model=MembershipPlanResponse)
    async def update_plan(self, plan_id: UUID, request: MembershipPlanSchema):
        try:
            updated_count = await MembershipPlan.filter(id=plan_id).update(
                **request.dict(exclude_unset=True)
            )
            if not updated_count:
                raise HTTPException(status_code=404, detail="Plan not found")

            plan = await MembershipPlan.get(id=plan_id)
            return plan
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e)) from e

    @router.delete("/{plan_id}/", response_model=BaseResponse)
    async def delete_plan(self, plan_id: UUID):
        try:
            plan = await MembershipPlan.get_or_none(id=plan_id)
            if not plan:
                raise HTTPException(status_code=404, detail="Plan not found")

            await plan.delete()
            return BaseResponse(message=f"Membership plan '{plan.name}' deleted successfully")
        except Exception as e: 
            raise HTTPException(status_code=500, detail=str(e)) from e
