from accounts import services as accounts_services
from accounts.schemas import UserResponseSchema
from authentication import schemas
from authentication.utils import authenticate_user, create_access_token
from fastapi import HTTPException, status
from fastapi_utils.inferring_router import InferringRouter

router = InferringRouter(prefix="/auth", tags=["auth"])


@router.post("/register/", response_model=UserResponseSchema)
async def register(form_data: schemas.UserRegistrationForm):
    try:
        user = await accounts_services.create_user(form_data)
        return user
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e),
        )


@router.post("/gimme-jwt/", response_model=schemas.Token)
async def gimme_jwt(form_data: schemas.LoginForm) -> schemas.Token:
    user = await authenticate_user(form_data.email, form_data.password)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )

    access_token = create_access_token(data={"sub": user.email})
    return schemas.Token(access_token=access_token, token_type="bearer")
