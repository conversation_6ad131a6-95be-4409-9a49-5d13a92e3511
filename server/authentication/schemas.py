from pydantic import BaseModel
from pydantic import EmailStr
from pydantic import Field
from typing import Optional
from accounts.models import User


class Token(BaseModel):
    access_token: str
    token_type: str


class TokenData(BaseModel):
    email: str | None = None


class UserRegistrationForm(BaseModel):
    email: EmailStr = Field(description="Email address")
    password: str = Field(min_length=8, max_length=1000)
    first_name: str = Field(min_length=3, max_length=64)
    last_name: str = Field(min_length=3, max_length=64, default=None)
    phone: str = Field(min_length=10, max_length=15, default=None)
    is_active: bool = Field(default=True)
    role: Optional[str] = Field(
        default=User.ROLE.STAFF,
        description=f"Available roles: {', '.join(User.ROLE.ALL)}"
    )



class LoginForm(BaseModel):
    email: EmailStr
    password: str = Field(min_length=8, max_length=64)
