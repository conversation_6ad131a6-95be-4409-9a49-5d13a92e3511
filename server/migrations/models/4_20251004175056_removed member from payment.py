from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "payments" DROP CONSTRAINT IF EXISTS "fk_payments_members_39c5b3bb";
        ALTER TABLE "payments" DROP COLUMN "member_id";"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "payments" ADD "member_id" UUID NOT NULL;
        ALTER TABLE "payments" ADD CONSTRAINT "fk_payments_members_39c5b3bb" FOREIGN KEY ("member_id") REFERENCES "members" ("id") ON DELETE CASCADE;"""
