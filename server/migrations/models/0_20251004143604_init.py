from tortoise import BaseDBAsync<PERSON>lient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        CREATE TABLE IF NOT EXISTS "users" (
    "id" UUID NOT NULL PRIMARY KEY,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "first_name" VARCHAR(255) NOT NULL,
    "last_name" VARCHAR(255),
    "email" VARCHAR(255) UNIQUE,
    "phone" VARCHAR(20),
    "password" TEXT,
    "is_active" BOOL NOT NULL DEFAULT True,
    "role" VARCHAR(20) NOT NULL DEFAULT 'staff'
);
CREATE TABLE IF NOT EXISTS "membershipplan" (
    "id" UUID NOT NULL PRIMARY KEY,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "name" VARCHAR(100) NOT NULL UNIQUE,
    "price" DECIMAL(8,2) NOT NULL,
    "duration_days" INT NOT NULL,
    "description" TEXT
);
CREATE TABLE IF NOT EXISTS "members" (
    "id" UUID NOT NULL PRIMARY KEY,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "name" VARCHAR(255) NOT NULL,
    "email" VARCHAR(255) UNIQUE,
    "phone" VARCHAR(20),
    "date_of_birth" DATE,
    "gender" VARCHAR(10),
    "address" TEXT,
    "emergency_contact" VARCHAR(255),
    "membership_start_date" DATE,
    "membership_end_date" DATE,
    "is_active" BOOL NOT NULL DEFAULT True,
    "membership_plan_id" UUID REFERENCES "membershipplan" ("id") ON DELETE CASCADE
);
CREATE TABLE IF NOT EXISTS "attendance" (
    "id" UUID NOT NULL PRIMARY KEY,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "check_in_time" TIMESTAMPTZ NOT NULL,
    "check_out_time" TIMESTAMPTZ,
    "duration_minutes" INT,
    "notes" TEXT,
    "member_id" UUID NOT NULL REFERENCES "members" ("id") ON DELETE CASCADE
);
CREATE TABLE IF NOT EXISTS "payments" (
    "id" UUID NOT NULL PRIMARY KEY,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "amount" DECIMAL(10,2) NOT NULL,
    "payment_date" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "payment_method" VARCHAR(50) NOT NULL,
    "transaction_id" VARCHAR(100),
    "status" VARCHAR(20) NOT NULL DEFAULT 'pending',
    "notes" TEXT,
    "member_id" UUID NOT NULL REFERENCES "members" ("id") ON DELETE CASCADE,
    "membership_plan_id" UUID REFERENCES "membershipplan" ("id") ON DELETE CASCADE
);
CREATE TABLE IF NOT EXISTS "equipment_categories" (
    "id" UUID NOT NULL PRIMARY KEY,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "name" VARCHAR(100) NOT NULL UNIQUE,
    "description" TEXT
);
CREATE TABLE IF NOT EXISTS "equipment" (
    "id" UUID NOT NULL PRIMARY KEY,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "name" VARCHAR(255) NOT NULL,
    "brand" VARCHAR(100),
    "serial_number" VARCHAR(100) UNIQUE,
    "purchase_date" DATE,
    "purchase_price" DECIMAL(10,2),
    "status" VARCHAR(20) NOT NULL DEFAULT 'operational',
    "condition" VARCHAR(20) NOT NULL DEFAULT 'excellent',
    "location" VARCHAR(100),
    "last_maintenance_date" DATE,
    "next_maintenance_date" DATE,
    "quantity" INT NOT NULL DEFAULT 1,
    "is_available" BOOL NOT NULL DEFAULT True,
    "category_id" UUID REFERENCES "equipment_categories" ("id") ON DELETE CASCADE
);
CREATE TABLE IF NOT EXISTS "maintenance_logs" (
    "id" UUID NOT NULL PRIMARY KEY,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "maintenance_type" VARCHAR(50) NOT NULL,
    "description" TEXT NOT NULL,
    "cost" DECIMAL(10,2),
    "performed_by" VARCHAR(255),
    "performed_date" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "next_scheduled" DATE,
    "equipment_id" UUID NOT NULL REFERENCES "equipment" ("id") ON DELETE CASCADE
);
CREATE TABLE IF NOT EXISTS "gym_classes" (
    "id" UUID NOT NULL PRIMARY KEY,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "name" VARCHAR(255) NOT NULL,
    "description" TEXT,
    "capacity" INT NOT NULL,
    "duration_minutes" INT NOT NULL,
    "day_of_week" VARCHAR(20) NOT NULL,
    "start_time" TIMETZ NOT NULL,
    "is_active" BOOL NOT NULL DEFAULT True,
    "trainer_id" UUID REFERENCES "users" ("id") ON DELETE CASCADE
);
CREATE TABLE IF NOT EXISTS "class_bookings" (
    "id" UUID NOT NULL PRIMARY KEY,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "booking_date" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "class_date" DATE NOT NULL,
    "status" VARCHAR(20) NOT NULL DEFAULT 'confirmed',
    "gym_class_id" UUID NOT NULL REFERENCES "gym_classes" ("id") ON DELETE CASCADE,
    "member_id" UUID NOT NULL REFERENCES "members" ("id") ON DELETE CASCADE,
    CONSTRAINT "uid_class_booki_gym_cla_99184a" UNIQUE ("gym_class_id", "member_id", "class_date")
);
CREATE TABLE IF NOT EXISTS "aerich" (
    "id" SERIAL NOT NULL PRIMARY KEY,
    "version" VARCHAR(255) NOT NULL,
    "app" VARCHAR(100) NOT NULL,
    "content" JSONB NOT NULL
);"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        """
