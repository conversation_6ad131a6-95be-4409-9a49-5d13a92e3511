from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "payments" ADD "attendance_id" UUID;
        ALTER TABLE "payments" ADD CONSTRAINT "fk_payments_attendan_fb997249" FOREIGN KEY ("attendance_id") REFERENCES "attendance" ("id") ON DELETE CASCADE;"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "payments" DROP CONSTRAINT IF EXISTS "fk_payments_attendan_fb997249";
        ALTER TABLE "payments" DROP COLUMN "attendance_id";"""
