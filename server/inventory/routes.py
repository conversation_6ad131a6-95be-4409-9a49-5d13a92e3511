from uuid import UUID
from fastapi import APIRouter, Depends, HTTPException, Query
from fastapi_utils.cbv import cbv
from typing import Optional
from inventory.models import Equipment, EquipmentCategory, MaintenanceLog
from inventory.schemas import (
     EquipmentCreateSchema, EquipmentResponse,
     MaintenanceLogCreateSchema, MaintenanceLogResponse,
    EquipmentCategoryResponseSchema, EquipmentCategoryCreateSchema
)
from authentication.utils import get_current_active_user
from schemas import BaseResponse
from fastapi_pagination import Page, paginate
from tortoise.transactions import in_transaction


router = APIRouter(prefix="/inventory", tags=["inventory"], 
                   dependencies=[Depends(get_current_active_user)])


@cbv(router)
class EquipmentCategoryCBV:

    @router.post("/categories/", response_model=EquipmentCategoryResponseSchema)
    async def create_category(self, request: EquipmentCategoryCreateSchema):
        try:
            category = await EquipmentCategory.create(**request.dict())
            return category
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e)) from e

    @router.get("/categories/", response_model=list[EquipmentCategoryResponseSchema])
    async def get_categories(self):
        try:
            categories = await EquipmentCategory.all()
            return categories
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e)) from e

    @router.get("/categories/{category_id}/", response_model=EquipmentCategoryResponseSchema)
    async def get_category(self, category_id: UUID):
        try:
            category = await EquipmentCategory.get_or_none(id=category_id)
            if not category:
                raise HTTPException(status_code=404, detail="Category not found")
            return category
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e)) from e

    @router.put("/categories/{category_id}/", response_model=EquipmentCategoryResponseSchema)
    async def update_category(self, category_id: UUID, request: EquipmentCategoryCreateSchema):
        try:
            updated_count = await EquipmentCategory.filter(id=category_id).update(**request.dict(exclude_unset=True))
            if not updated_count:
                raise HTTPException(status_code=404, detail="Category not found")

            category = await EquipmentCategory.get(id=category_id)
            return category
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e)) from e

    @router.delete("/categories/{category_id}/", response_model=BaseResponse)
    async def delete_category(self, category_id: UUID):
        try:
            category = await EquipmentCategory.get_or_none(id=category_id)
            if not category:
                raise HTTPException(status_code=404, detail="Category not found")

            await category.delete()
            return BaseResponse(message=f"Category '{category.name}' deleted successfully")
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e)) from e


@cbv(router)
class EquipmentCBV:

    @router.post("/equipment/", response_model=EquipmentResponse)
    async def create_equipment(self, request: EquipmentCreateSchema):
        try:
            data = request.dict(exclude={'category_id'})
            if request.category_id:
                category = await EquipmentCategory.get_or_none(id=request.category_id)
                if not category:
                    raise HTTPException(status_code=404, detail="Category not found")
                data['category_id'] = request.category_id
            
            equipment = await Equipment.create(**data)
            equipment = await Equipment.get(id=equipment.id).prefetch_related('category')
            return equipment
        except HTTPException:
            raise
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))  from e

    @router.get("/equipment/", response_model=Page[EquipmentResponse])
    async def get_equipment(
        self,
        status: Optional[Equipment.StatusLiteral] = Query(None),
        category_id: Optional[UUID] = Query(None),
        is_available: Optional[bool] = Query(None),
        search: Optional[str] = Query(None)
    ):
        try:
            query = Equipment.all()
            
            if status:
                query = query.filter(status=status)
            if category_id:
                query = query.filter(category_id=category_id)
            if is_available is not None:
                query = query.filter(is_available=is_available)
            if search:
                query = query.filter(name__icontains=search)
            
            equipment = await query.prefetch_related('category')
            return paginate(equipment)
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e)) from e

    @router.get("/equipment/{equipment_id}/", response_model=EquipmentResponse)
    async def get_equipment_detail(self, equipment_id: UUID):
        try:
            equipment = await Equipment.get_or_none(id=equipment_id).prefetch_related('category')
            if not equipment:
                raise HTTPException(status_code=404, detail="Equipment not found")
            return equipment
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e)) from e

    @router.put("/equipment/{equipment_id}/", response_model=EquipmentResponse)
    async def update_equipment(self, equipment_id: UUID, request: EquipmentCreateSchema):
        try:
            data = request.dict(exclude_unset=True, exclude={'category_id'})
            if request.category_id:
                data['category_id'] = request.category_id
            
            updated_count = await Equipment.filter(id=equipment_id).update(**data)
            if not updated_count:
                raise HTTPException(status_code=404, detail="Equipment not found")
            
            equipment = await Equipment.get(id=equipment_id).prefetch_related('category')
            return equipment
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e)) from e

    @router.delete("/equipment/{equipment_id}/", response_model=BaseResponse)
    async def delete_equipment(self, equipment_id: UUID):
        try:
            equipment = await Equipment.get_or_none(id=equipment_id)
            if not equipment:
                raise HTTPException(status_code=404, detail="Equipment not found")
            
            await equipment.delete()
            return BaseResponse(message=f"Equipment '{equipment.name}' deleted successfully")
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e)) from e

    
    @router.post("/equipment/{equipment_id}/maintenance/", response_model=MaintenanceLogResponse)
    async def log_maintenance(self, equipment_id: UUID, request: MaintenanceLogCreateSchema):
        try:
            async with in_transaction() as conn:
                equipment = await Equipment.get_or_none(id=equipment_id, using_db=conn)
                if not equipment:
                    raise HTTPException(status_code=404, detail="Equipment not found")
                
                maintenance = await MaintenanceLog.create(
                    equipment_id=equipment_id, 
                    **request.dict(),
                    using_db=conn
                )
                
                await Equipment.filter(id=equipment_id).using_db(conn).update(
                    last_maintenance_date=maintenance.performed_date.date(),
                    next_maintenance_date=request.next_scheduled
                )
                
                return await MaintenanceLog.get(id=maintenance.id).prefetch_related('equipment__category')
        except HTTPException:
            raise
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e)) from e
        
    @router.get("/equipment/{equipment_id}/maintenance/", response_model=Page[MaintenanceLogResponse])
    async def get_maintenance_history(self, equipment_id: UUID):
        try:
            logs = await MaintenanceLog.filter(equipment_id=equipment_id).order_by('-performed_date').prefetch_related('equipment__category')
            return paginate(logs)
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e)) from e
