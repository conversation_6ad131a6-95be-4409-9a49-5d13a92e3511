from typing import List, Optional
from authentication.schemas import UserRegistrationForm
from authentication.utils import get_password_hash
from accounts.models import User


async def get_user(user_id: int) -> Optional[User]:
    return await User.get_or_none(id=user_id)


async def get_user_by_email(email: str) -> Optional[User]:
    return await User.get_or_none(email=email)


async def get_users(skip: int = 0, limit: int = 100) -> List[User]:
    return await User.all().offset(skip).limit(limit)


async def create_user(user: UserRegistrationForm) -> User:
    hashed_password = get_password_hash(user.password)

    # check if user already exists
    existing_user = await User.get_or_none(email=user.email)
    if existing_user:
        raise ValueError("User already exists")

    db_user = User(
        email=user.email,
        password=hashed_password,
        first_name=user.first_name,
        last_name=user.last_name,
        phone=user.phone,
        is_active=user.is_active,
        role=user.role,
    )
    await db_user.save()

    return db_user
