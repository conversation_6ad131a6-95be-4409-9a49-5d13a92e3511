from core.models import BaseModel,models
from typing import Literal


class User(BaseModel):
    class ROLE:
        ADMIN = "admin"
        STAFF = "staff"
        TRAINER = "trainer"
        RECEPTIONIST = "receptionist"
        MANAGER = "manager"
        ACCOUNTANT = "accountant"

        CHOICES = [
            (<PERSON><PERSON><PERSON>, "Admin"),
            (<PERSON><PERSON><PERSON>, "Staff"),
            (<PERSON><PERSON><PERSON><PERSON>, "Trainer"),
            (RECEP<PERSON>ONIS<PERSON>, "Receptionist"),
            (<PERSON><PERSON><PERSON>R, "Manager"),
            (ACCOUNTANT, "Accountant"),
        ]

        ALL = [ADMIN, STAFF, TRAINER, RECEPTIONIST, MANAGER, ACCOUNTANT]

    RoleLiteral = Literal[tuple(ROLE.ALL)]



    first_name = models.Char<PERSON>ield(max_length=255)
    last_name = models.Char<PERSON>ield(max_length=255, null=True)
    email = models.Char<PERSON>ield(max_length=255, unique=True, null=True)
    phone = models.CharField(max_length=20, null=True)
    password = models.TextField(null=True)
    is_active = models.BooleanField(default=True)
    role = models.CharField(max_length=20, choices=ROLE.CHOICES, default=ROLE.STAFF)

    class Meta:
        table = "users"

