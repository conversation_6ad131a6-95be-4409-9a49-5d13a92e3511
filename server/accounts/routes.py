from uuid import UUID
from fastapi import APIRouter, Depends, HTTPException, Query, status, Request
from fastapi_utils.cbv import cbv
from typing import Optional, List
from tortoise.exceptions import DoesNotExist
from fastapi_pagination import Page, paginate

from authentication.utils import get_current_active_user
from accounts.models import User
from accounts.schemas import UserSchema, UserResponseSchema
from schemas import BaseResponse

router = APIRouter(
    prefix="/accounts",
    tags=["accounts"],
    dependencies=[Depends(get_current_active_user)],
)


@cbv(router)
class UserCBV:


    @router.get("/", response_model=Page[UserResponseSchema])
    async def list_users(
        self,
        is_active: Optional[bool] = Query(None),
        email: Optional[str] = Query(None),
        phone: Optional[str] = Query(None),
        role: Optional[User.RoleLiteral] = Query(None),
    ):
        try:
            query = User.all()

            if is_active is not None:
                query = query.filter(is_active=is_active)
            if email:
                query = query.filter(email=email)
            if phone:
                query = query.filter(phone=phone)
            if role:
                query = query.filter(role=role)

            users = await query
            return paginate(users)
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e)) from e



    @router.put("/{user_id}/", response_model=UserResponseSchema)
    async def update_user(self, user_id: UUID, request: UserSchema):
        try:
            user = await User.get_or_none(id=user_id)
            if not user:
                raise HTTPException(status_code=404, detail="User not found")

            data = request.dict(exclude_unset=True)
            data.pop("password", None)
               

            await User.filter(id=user_id).update(**data)
            updated_user = await User.get(id=user_id)
            return updated_user
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e)) from e

    @router.delete("/{user_id}/", response_model=BaseResponse)
    async def delete_user(self, user_id: UUID):
        try:
            user = await User.get_or_none(id=user_id)
            if not user:
                raise HTTPException(status_code=404, detail="User not found")

            await user.delete()
            return BaseResponse(message=f"User '{user.first_name}' deleted successfully")
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e)) from e
        
        
    
    @router.get("/me/", response_model=UserResponseSchema)
    async def get_me(self, request: Request):
        
        try:
            
            current_user = request.state.user  
            if not current_user:
                raise HTTPException(status_code=401, detail="Not authenticated")
            return current_user
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e)) from e
