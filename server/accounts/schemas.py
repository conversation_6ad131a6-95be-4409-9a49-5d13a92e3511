
from pydantic import EmailStr
from typing import Optional
from pydantic import Field
from core.schema import BaseSchema
from accounts.models import User

class UserSchema(BaseSchema):
    first_name: Optional[str] = Field(None)
    last_name: Optional[str] = Field(None)
    email: EmailStr
    phone: Optional[str] = Field(None)
    is_active: Optional[bool] = Field(None)
    password: Optional[str] = Field(None, min_length=8, max_length=1000)
    role: Optional[User.RoleLiteral] = Field(
        default=User.ROLE.STAFF,
        description=f"Available roles: {', '.join(User.ROLE.ALL)}"
    )



class UserResponseSchema(BaseSchema):
    first_name: str
    last_name: Optional[str] = Field(None)
    email: EmailStr
    phone: Optional[str] = Field(None)
    is_active: Optional[bool] = Field(None)
    role: Optional[User.RoleLiteral] = Field(
        default=User.ROLE.STAFF,
        description=f"Available roles: {', '.join(User.ROLE.ALL)}"
    )

