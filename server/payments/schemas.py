from typing import Optional
from datetime import datetime
from core.schema import BaseSchema,BaseCreateSchema
from uuid import UUID
from members.schemas import MemberResponseSchema
from decimal import Decimal
from payments.models import Payment


class PaymentCreateSchema(BaseCreateSchema):
    member_id: UUID
    payment_method: str
    attendance_id: Optional[UUID] = None 
    transaction_id: Optional[str] = None
    notes: Optional[str] = None


class PaymentResponseSchema(BaseSchema):
    member: Optional[MemberResponseSchema] = None
    amount: Optional[Decimal] = None
    payment_date: datetime
    payment_method: str
    transaction_id: Optional[str] = None
    status: Payment.PaymentStatusLiteral
    notes: Optional[str] = None
    paid: bool = False
    attendance_id: Optional[UUID] = None

    class Config:
        from_attributes = True
