from uuid import UUID
from datetime import date
from fastapi import APIRouter, Depends, HTTPException, Query
from fastapi_utils.cbv import cbv
from typing import Optional
from payments.models import Payment
from payments.schemas import PaymentCreateSchema, PaymentResponseSchema
from members.models import Member
from attendance.models import Attendance
from authentication.utils import get_current_active_user
from schemas import BaseResponse
from fastapi_pagination import Page, paginate

# router = APIRouter(prefix="/payments", tags=["payments"], 
#                    dependencies=[Depends(get_current_active_user)])



router = APIRouter(prefix="/payments", tags=["payments"])


@cbv(router)
class PaymentCBV:

    @router.post("/", response_model=PaymentResponseSchema)
    async def create_payment(self, request: PaymentCreateSchema):
        try:
            member = await Member.get_or_none(id=request.member_id).prefetch_related('membership_plan')
            if not member:
                raise HTTPException(status_code=404, detail="Member not found")
            
            if not member.is_active:
                raise HTTPException(status_code=400, detail="Member account is inactive")
            
            if not member.membership_plan:
                raise HTTPException(
                    status_code=400, 
                    detail="Member has no membership plan assigned"
                )
            
            data = request.dict()
            
            data['amount'] = member.membership_plan.price
            
            data['status'] = Payment.STATUS.PAID
            
            if request.attendance_id:
                attendance = await Attendance.get_or_none(id=request.attendance_id)
                
                
                if not attendance:
                    raise HTTPException(
                        status_code=404, 
                        detail="Attendance record not found"
                    )
                
                if attendance.member_id != request.member_id:
                    raise HTTPException(
                        status_code=400, 
                        detail="Attendance record does not belong to this member"
                    )
                
                existing_payment = await Payment.get_or_none(attendance_id=request.attendance_id)
                if existing_payment:
                    raise HTTPException(
                        status_code=400, 
                        detail=f"Payment already exists for this attendance session (Payment ID: {existing_payment.id})"
                    )
                
                data['attendance_id'] = request.attendance_id
            else:
                data['attendance_id'] = None
            
    
            payment = await Payment.create(**data)
            
            if request.attendance_id:
                await Attendance.filter(id=request.attendance_id).update(paid=True)
            
            payment = await Payment.get(id=payment.id).prefetch_related(
                'member__membership_plan',
                'attendance'
            )
            
            return payment
            
        except HTTPException:
            raise
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e)) from e
        

    @router.get("/", response_model=Page[PaymentResponseSchema])
    async def get_payments(
        self,
        member_id: Optional[UUID] = Query(None),
        status: Optional[Payment.PaymentStatusLiteral] = Query(None),
        start_date: Optional[date] = Query(None),
        end_date: Optional[date] = Query(None)
        
    ):
        try:
            query = Payment.all()
            
            
            if member_id:
                query = query.filter(member_id=member_id)
            if status:
                query = query.filter(status=status)
            if start_date:
                query = query.filter(payment_date__gte=start_date)
            if end_date:
                query = query.filter(payment_date__lte=end_date)
          
                
            
            
            payments = await query.prefetch_related('member__membership_plan', 'attendance').order_by('-payment_date')
            return paginate(payments)
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e)) from e

    @router.get("/{payment_id}/", response_model=PaymentResponseSchema)
    async def get_payment(self, payment_id: UUID):
        try:
            payment = await Payment.get_or_none(id=payment_id).prefetch_related('member__membership_plan', 'attendance')
            if not payment:
                raise HTTPException(status_code=404, detail="Payment not found")
            return payment
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e)) from e

    @router.delete("/{payment_id}/", response_model=BaseResponse)
    async def delete_payment(self, payment_id: UUID):
        try:
            payment = await Payment.get_or_none(id=payment_id)
            if not payment:
                raise HTTPException(status_code=404, detail="Payment not found")

            await payment.delete()
            return BaseResponse(message="Payment deleted successfully")
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e)) from e
        
        
    @router.get("/member/{member_id}/history", response_model=Page[PaymentResponseSchema])
    async def get_member_payment_history(
        self,
        member_id: UUID,
        payment_type: Optional[str] = Query(None),
        start_date: Optional[date] = Query(None),
        end_date: Optional[date] = Query(None)
    ):
        try:
            query = Payment.filter(member_id=member_id)
            
            if payment_type:
                query = query.filter(payment_type=payment_type)
            if start_date:
                query = query.filter(payment_date__gte=start_date)
            if end_date:
                query = query.filter(payment_date__lte=end_date)
            
            payments = await query.prefetch_related(
                'member__membership_plan',
                'attendance'
            ).order_by('-payment_date')
            
            return paginate(payments)
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e)) from e
