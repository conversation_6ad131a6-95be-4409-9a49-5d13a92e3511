from core.models import BaseModel,models
from typing import Literal


class Payment(BaseModel):
    
    class PAYMENT_TYPE:
        MEMBERSHIP = "membership"
        DAY_PASS = "day_pass"
        CLASS_BOOKING = "class_booking"
        PERSONAL_TRAINING = "personal_training"
        REGISTRATION = "registration"
        OTHER = "other"
    
    class STATUS:
        PENDING = "pending"
        PAID = "paid"
        FAILED = "failed"
        CANCELLED = "cancelled"
        REFUNDED = "refunded"
        CHOICES = [
            (PENDING, "pending"),
            (PAID, "paid"),
            (FAILED, "failed"),
            (REFUNDED, "refunded"),
            (CANCELLED, "cancelled"),
        ]
        ALL = [PENDING, PAID, FAILED, REFUNDED, CANCELLED]
        
    PaymentStatusLiteral = Literal[tuple(STATUS.ALL)]

    member = models.ForeignKeyField('models.Member')
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    payment_date = models.DatetimeField(auto_now_add=True)
    payment_method = models.CharField(max_length=50)
    transaction_id = models.CharField(max_length=100, null=True)
    status = models.CharField(max_length=20, default=STATUS.PENDING, choices=STATUS.CHOICES)
    notes = models.TextField(null=True)
    attendance = models.ForeignKeyField('models.Attendance', null=True)

    class Meta:
        table = "payments"
