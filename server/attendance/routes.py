from uuid import UUID
from datetime import datetime, date,timezone
from fastapi import APIRouter, Depends, HTTPException, Query
from fastapi_utils.cbv import cbv
from typing import Optional
from attendance.models import Attendance
from attendance.schemas import AttendanceCheckInSchema, AttendanceResponseSchema
from members.models import Member
from authentication.utils import get_current_active_user
from schemas import BaseResponse
from fastapi_pagination import Page, paginate


# router = APIRouter(prefix="/attendance", tags=["attendance"], 
#                    dependencies=[Depends(get_current_active_user)])


router = APIRouter(prefix="/attendance", tags=["attendance"])


@cbv(router)
class AttendanceCBV:
    @router.post("/check-in/", response_model=AttendanceResponseSchema)
    async def check_in(self, request: AttendanceCheckInSchema):
        try:
            attendance_data = {
                'member_id': request.member,
                'check_in_time': datetime.now(timezone.utc),
                'notes': request.notes,
            }

            member = await Member.get_or_none(id=request.member).prefetch_related('membership_plan')
            if not member:
                raise HTTPException(status_code=404, detail="Member not found")
            
            existing = await Attendance.filter(member_id=request.member, checked_out=True).first()
            if existing:
                raise HTTPException(status_code=400, detail="Member already checked in")
            
            
            if not member.is_active:
                raise HTTPException(status_code=400, detail="Member account is inactive")
            
            if member.membership_end_date and member.membership_end_date < date.today():
                raise HTTPException(status_code=400, detail="Membership has expired")
       
        
            
            
            

            attendance = await Attendance.create(**attendance_data)
            attendance = await Attendance.get(id=attendance.id).prefetch_related('member__membership_plan')
            return attendance
        except HTTPException:
            raise
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))
    

    @router.patch("/{attendance_id}/check-out/", response_model=AttendanceResponseSchema)
    async def check_out(self, attendance_id: UUID):
        try:
            attendance = await Attendance.get_or_none(id=attendance_id)
            if not attendance:
                raise HTTPException(status_code=404, detail="Attendance record not found")
            
            if  attendance.checked_out:
                raise HTTPException(status_code=400, detail="Already checked out")
            
            check_out_time = datetime.now(timezone.utc)
            duration = int((check_out_time - attendance.check_in_time).total_seconds() / 60)
            
            await Attendance.filter(id=attendance_id).update(
                check_out_time=check_out_time,
                duration_minutes=duration,
                checked_out=True
            )
            
            attendance = await Attendance.get(id=attendance_id).prefetch_related('member__membership_plan')
            return attendance
        except HTTPException:
            raise
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))

    @router.get("/", response_model=Page[AttendanceResponseSchema])
    async def get_attendance(
        self,
        member_id: Optional[UUID] = Query(None),
        start_date: Optional[date] = Query(None),
        end_date: Optional[date] = Query(None),
        checked_out: Optional[bool] = Query(None)
    ):
        try:
            query = Attendance.all()
            
            if member_id:
                query = query.filter(member_id=member_id)
            if start_date:
                query = query.filter(check_in_time__gte=start_date)
            if end_date:
                query = query.filter(check_in_time__lte=end_date)
            if checked_out is not None:
                query = query.filter(checked_out=checked_out)
            
            records = await query.prefetch_related('member__membership_plan').order_by('-check_in_time')
        
            return paginate(records)
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))

    @router.get("/{attendance_id}/", response_model=AttendanceResponseSchema)
    async def get_attendance_record(self, attendance_id: UUID):
        try:
            attendance = await Attendance.get_or_none(id=attendance_id).prefetch_related('member__membership_plan')
            if not attendance:
                raise HTTPException(status_code=404, detail="Attendance record not found")
            return attendance
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))

    @router.delete("/{attendance_id}/", response_model=BaseResponse)
    async def delete_attendance(self, attendance_id: UUID):
        try:
            attendance = await Attendance.get_or_none(id=attendance_id)
            if not attendance:
                raise HTTPException(status_code=404, detail="Attendance record not found")

            await attendance.delete()
            return BaseResponse(message="Attendance record deleted successfully")
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))
