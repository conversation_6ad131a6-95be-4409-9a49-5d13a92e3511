from typing import Optional
from datetime import datetime
from uuid import UUID
from core.schema import BaseSchema,BaseCreateSchema
from members.schemas import MemberResponseSchema


class AttendanceCheckInSchema(BaseCreateSchema):
    member: UUID
    notes: Optional[str] = None



class AttendanceResponseSchema(BaseSchema):
    check_in_time: datetime
    check_out_time: Optional[datetime] = None
    duration_minutes: Optional[int] = None
    notes: Optional[str] = None
    checked_out: bool = False
    member: Optional[MemberResponseSchema] = None 

    class Config:
        from_attributes = True
 

    
    

