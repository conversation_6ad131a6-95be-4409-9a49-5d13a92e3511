from core.models import BaseModel,models


class Attendance(BaseModel):
    member = models.ForeignKeyField('models.Member')
    check_in_time = models.DatetimeField()
    check_out_time = models.DatetimeField(null=True)
    duration_minutes = models.IntField(null=True)
    notes = models.TextField(null=True)
    checked_out = models.BooleanField(default=False)


    class Meta:
        table = "attendance"
