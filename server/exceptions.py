class BusinessRuleException(Exception):
    """Base exception for business rule violations"""
    def __init__(self, message: str, status_code: int = 400):
        self.message = message
        self.status_code = status_code
        super().__init__(self.message)

class MembershipExpiredException(BusinessRuleException):
    def __init__(self):
        super().__init__("Membership has expired", 403)

class AlreadyCheckedInException(BusinessRuleException):
    def __init__(self):
        super().__init__("Member already checked in", 400)

