from uuid import UUID
from datetime import date
from fastapi import APIRouter, Depends, HTTPException, Query
from fastapi_utils.cbv import cbv
from typing import Optional
from classes.models import GymClass, ClassBooking
from classes.schemas import (
    GymClassCreateSchema,  GymClassResponse,
    ClassBookingCreateSchema, ClassBookingSchema, ClassBookingResponse
)
from accounts.models import User
from members.models import Member
from authentication.utils import get_current_active_user
from schemas import BaseResponse
from fastapi_pagination import Page, paginate

# router = APIRouter(prefix="/classes", tags=["classes"], 
#                    dependencies=[Depends(get_current_active_user)])



router = APIRouter(prefix="/classes", tags=["classes"])


@cbv(router)
class GymClassCBV:

    @router.post("/", response_model=GymClassResponse)
    async def create_class(self, request: GymClassCreateSchema):
        try:
            data = request.dict(exclude={'trainer_id'})
            

            if request.trainer_id:
                trainer = await User.get_or_none(id=request.trainer_id, role=User.ROLE.TRAINER)
                if not trainer:
                    raise HTTPException(status_code=404, detail="Trainer not found")
                data['trainer_id'] = request.trainer_id
            
            gym_class = await GymClass.create(**data)
            gym_class = await GymClass.get(id=gym_class.id).prefetch_related('trainer')
            return gym_class
        except HTTPException:
            raise
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e)) from e
 
    @router.get("/", response_model=Page[GymClassResponse])
    async def get_classes(
        self,
        is_active: Optional[bool] = Query(None),
        trainer_id: Optional[UUID] = Query(None),
        day_of_week: Optional[str] = Query(None)
    ):
        try:
            query = GymClass.all()
            
            if is_active is not None:
                query = query.filter(is_active=is_active)
            if trainer_id:
                query = query.filter(trainer_id=trainer_id)
            if day_of_week:
                query = query.filter(day_of_week=day_of_week)
            
            classes = await query.prefetch_related('trainer')
            return paginate(classes)
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e)) from e

    @router.get("/{class_id}/", response_model=GymClassResponse)
    async def get_class(self, class_id: UUID):
        try:
            gym_class = await GymClass.get_or_none(id=class_id).prefetch_related('trainer')
            if not gym_class:
                raise HTTPException(status_code=404, detail="Class not found")
            return gym_class
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e)) from e

    @router.put("/{class_id}/", response_model=GymClassResponse)
    async def update_class(self, class_id: UUID, request: GymClassCreateSchema):
        try:
            data = request.dict(exclude_unset=True, exclude={'trainer_id'})
            if request.trainer_id:
                data['trainer_id'] = request.trainer_id
                
        
            updated_count = await GymClass.filter(id=class_id).update(**data)
            if not updated_count:
                raise HTTPException(status_code=404, detail="Class not found")

            gym_class = await GymClass.get(id=class_id).prefetch_related('trainer')
            return gym_class
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e)) from e

    @router.delete("/{class_id}/", response_model=BaseResponse)
    async def delete_class(self, class_id: UUID):
        try:
            gym_class = await GymClass.get_or_none(id=class_id)
            if not gym_class:
                raise HTTPException(status_code=404, detail="Class not found")

            await gym_class.delete()
            return BaseResponse(message=f"Class '{gym_class.name}' deleted successfully")
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e)) from e


@cbv(router)
class ClassBookingCBV:

    @router.post("/bookings/", response_model=ClassBookingResponse)
    async def create_booking(self, request: ClassBookingCreateSchema):
        try:
            gym_class = await GymClass.get_or_none(id=request.gym_class_id)
            if not gym_class:
                raise HTTPException(status_code=404, detail="Class not found")
            
            member = await Member.get_or_none(id=request.member_id)
            if not member:
                raise HTTPException(status_code=404, detail="Member not found")
            
            if not member.is_active:
                raise HTTPException(status_code=400, detail="Member is inactive")
            
            
            if member.membership_end_date and member.membership_end_date < request.class_date:
                raise HTTPException(status_code=400, detail="Membership will be expired on class date")
       
       
            
            booking_count = await ClassBooking.filter(
                gym_class_id=request.gym_class_id,
                class_date=request.class_date,
                status=ClassBooking.STATUS.CONFIRMED
            ).count()
            
            if booking_count >= gym_class.capacity:
                raise HTTPException(status_code=400, detail="Class is full")
            
            existing = await ClassBooking.get_or_none(
                gym_class_id=request.gym_class_id,
                member_id=request.member_id,
                class_date=request.class_date
            )
            if existing:
                raise HTTPException(status_code=400, detail="Already booked for this class")
            
            booking = await ClassBooking.create(**request.dict())
            booking = await ClassBooking.get(id=booking.id).prefetch_related('gym_class', 'member')
            return booking
        except HTTPException:
            raise
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e)) from e

    @router.get("/bookings", response_model=Page[ClassBookingResponse])
    async def get_bookings(
        self,
        member_id: Optional[UUID] = Query(None),
        gym_class_id: Optional[UUID] = Query(None),
        class_date: Optional[date] = Query(None),
        status: Optional[ClassBooking.StatusLiteral] = Query(None)
    ):
        try:
            
            
            query = ClassBooking.all()
            
            if member_id:
                query = query.filter(member_id=member_id)
            if gym_class_id:
                query = query.filter(gym_class_id=gym_class_id)
            if class_date:
                query = query.filter(class_date=class_date)
            if status:
                query = query.filter(status=status)
            
            bookings = await query.prefetch_related('gym_class', 'member').order_by('-booking_date')
            return paginate(bookings)
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e)) from e

    @router.get("/bookings/{booking_id}/", response_model=ClassBookingResponse)
    async def get_booking(self, booking_id: UUID):
        try:
            booking = await ClassBooking.get_or_none(id=booking_id).prefetch_related('gym_class', 'member')
            if not booking:
                raise HTTPException(status_code=404, detail="Booking not found")
            return booking
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e)) from e

    @router.patch("/bookings/{booking_id}/cancel/", response_model=ClassBookingResponse)
    async def cancel_booking(self, booking_id: UUID):
        try:
            updated_count = await ClassBooking.filter(id=booking_id).update(status=ClassBooking.STATUS.CANCELLED)
            if not updated_count:
                raise HTTPException(status_code=404, detail="Booking not found")

            booking = await ClassBooking.get(id=booking_id).prefetch_related('gym_class', 'member')
            return booking
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e)) from e

    @router.delete("/bookings/{booking_id}/", response_model=BaseResponse)
    async def delete_booking(self, booking_id: UUID):
        try:
            booking = await ClassBooking.get_or_none(id=booking_id)
            if not booking:
                raise HTTPException(status_code=404, detail="Booking not found")

            await booking.delete()
            return BaseResponse(message="Booking deleted successfully")
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e)) from e
        
        
    
    @router.patch("/bookings/{booking_id}/", response_model=ClassBookingResponse)
    async def update_booking(self, booking_id: UUID, request: ClassBookingCreateSchema):
      
        try:
            booking = await ClassBooking.get_or_none(id=booking_id)
            if not booking:
                raise HTTPException(status_code=404, detail="Booking not found")

            update_data = request.dict(exclude_unset=True)
            for field, value in update_data.items():
                setattr(booking, field, value)

            await booking.save()
            booking = await ClassBooking.get(id=booking.id).prefetch_related('gym_class', 'member')
            return booking
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e)) from e

