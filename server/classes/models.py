from core.models import BaseModel,models
from typing import Literal


class GymClass(BaseModel):
    name = models.CharField(max_length=255)
    description = models.TextField(null=True)
    trainer = models.ForeignKeyField('models.User', null=True) 
    capacity = models.IntField()
    duration_minutes = models.IntField()
    day_of_week = models.CharField(max_length=20)
    start_time = models.TimeField()
    is_active = models.BooleanField(default=True)

    class Meta:
        table = "gym_classes"


class ClassBooking(BaseModel):
    class STATUS:
        PENDING = "pending"
        CONFIRMED = "confirmed"
        CHECKED_IN = "checked_in"
        COMPLETED = "completed"
        CANCELLED = "cancelled"
        NO_SHOW = "no_show"

        CHOICES = [
            (PENDING, "Pending"),
            (CONFIRMED, "Confirmed"),
            (CHECKED_IN, "Checked In"),
            (COMPLETED, "Completed"),
            (CANCELLED, "Cancelled"),
            (NO_SHOW, "No Show"),
        ]

        ALL = [PENDING, CONFIRMED, CHECKED_IN, COMPLETED, CANCELLED, NO_SHOW]
        
    
    StatusLiteral = Literal[tuple(STATUS.ALL)]


    gym_class = models.ForeignKeyField('models.GymClass')
    member = models.ForeignKeyField('models.Member')
    booking_date = models.DatetimeField(auto_now_add=True)
    class_date = models.DateField()
    status = models.CharField(max_length=20, choices=STATUS.CHOICES, default=STATUS.CONFIRMED)

    class Meta:
        table = "class_bookings"
        unique_together = (("gym_class", "member", "class_date"),)
