# Gym Management System - Frontend Implementation Guide

# Not that u can reference all needed data in the server folder, it has all the apis and the code for the apis

# I dont like too many comments, add comments if only needed else dont , use proper naming conventions, the UI should be responsive especiality the members side of the ui because they will use phone most of the time and tablet sometimes pc but not compared to phone and tablet, not that the portions that list data are paginated using from fastapi_pagination import Page, paginate, memebers ont have to log in

## System Overview

**Authentication Model:**

- **Users (Staff)**: Trainers, Receptionists, Managers, Admins - have login credentials and role-based access
- **Members (Customers)**: No login required - identified by member ID/phone for bookings and payments

**Core User Flows:**

1. **Staff Portal**: Login → Dashboard → Manage operations
2. **Member Kiosk/Reception**: Select member → Book class/session → Make payment

---

## API Endpoints Reference

### Authentication

```
POST /auth/register/          - Register new staff user
POST /auth/gimme-jwt/         - Staff login (returns JWT token)
```

### Users (Staff Management)

```
GET  /accounts/               - List all staff users (filterable)
GET  /accounts/me/            - Get current logged-in user
PUT  /accounts/{user_id}/     - Update staff user
DELETE /accounts/{user_id}/   - Delete staff user
```

### Members

```
POST /members/                - Create new member
GET  /members/                - List members (search by name, filter by active)
GET  /members/{member_id}/    - Get member details
PUT  /members/{member_id}/    - Update member
DELETE /members/{member_id}/  - Delete member
```

### Membership Plans

```
POST /membership-plans/       - Create plan
GET  /membership-plans/       - List all plans (paginated)
GET  /membership-plans/{id}/  - Get plan details
PUT  /membership-plans/{id}/  - Update plan
DELETE /membership-plans/{id}/- Delete plan
```

### Attendance (Check-in/Check-out)

```
POST /attendance/check-in/    - Member check-in
PATCH /attendance/{id}/check-out/ - Member check-out
GET  /attendance/             - List attendance (filter by member, date, status)
GET  /attendance/{id}/        - Get attendance record
DELETE /attendance/{id}/      - Delete attendance record
```

### Payments

```
POST /payments/               - Create payment (member_id, payment_method, optional attendance_id)
GET  /payments/               - List payments (filter by member, status, date range)
GET  /payments/{id}/          - Get payment details
DELETE /payments/{id}/        - Delete payment
```

### Classes

```
POST /classes/                - Create gym class
GET  /classes/                - List classes (filter by active, trainer, day)
GET  /classes/{id}/           - Get class details
PUT  /classes/{id}/           - Update class
DELETE /classes/{id}/         - Delete class
```

### Class Bookings

```
POST /classes/bookings/       - Book class (gym_class_id, member_id, class_date)
GET  /classes/bookings        - List bookings (filter by member, class, date, status)
GET  /classes/bookings/{id}/  - Get booking details
PATCH /classes/bookings/{id}/cancel/ - Cancel booking
DELETE /classes/bookings/{id}/- Delete booking
```

### Inventory

```
POST /inventory/categories/   - Create equipment category
GET  /inventory/categories/   - List categories
PUT  /inventory/categories/{id}/ - Update category
DELETE /inventory/categories/{id}/ - Delete category

POST /inventory/equipment/    - Create equipment
GET  /inventory/equipment/    - List equipment (filter by status, category, availability)
GET  /inventory/equipment/{id}/ - Get equipment details
PUT  /inventory/equipment/{id}/ - Update equipment
DELETE /inventory/equipment/{id}/ - Delete equipment

POST /inventory/equipment/{id}/maintenance/ - Log maintenance
GET  /inventory/equipment/{id}/maintenance/ - Get maintenance history
```

---

## Required React Application Structure

### 1. Technology Stack

```json
{
  "core": ["React 18+", "TypeScript"],
  "routing": "React Router v6",
  "state_management": "React Query (TanStack Query) or Redux Toolkit",
  "ui_framework": "Material-UI, Ant Design, or Shadcn/ui",
  "forms": "React Hook Form + Zod validation",
  "http_client": "Axios",
  "date_handling": "date-fns or dayjs",
  "charts": "Recharts (for dashboard analytics)"
}
```

### 2. Project Structure

```
src/
├── api/
│   ├── axios.config.ts          # Axios instance with interceptors
│   ├── auth.api.ts              # Auth endpoints
│   ├── members.api.ts           # Member endpoints
│   ├── attendance.api.ts        # Attendance endpoints
│   ├── payments.api.ts          # Payment endpoints
│   ├── classes.api.ts           # Class & booking endpoints
│   ├── inventory.api.ts         # Inventory endpoints
│   └── users.api.ts             # User management endpoints
│
├── components/
│   ├── common/
│   │   ├── Layout.tsx           # Main layout with sidebar
│   │   ├── Navbar.tsx
│   │   ├── Sidebar.tsx
│   │   ├── LoadingSpinner.tsx
│   │   ├── ErrorBoundary.tsx
│   │   └── ProtectedRoute.tsx   # Role-based route protection
│   │
│   ├── members/
│   │   ├── MemberList.tsx       # Table with search/filter
│   │   ├── MemberForm.tsx       # Create/Edit form
│   │   ├── MemberDetails.tsx    # View member info
│   │   └── MemberSelector.tsx   # Quick search dropdown
│   │
│   ├── attendance/
│   │   ├── CheckInForm.tsx      # Member check-in
│   │   ├── AttendanceList.tsx   # Active sessions
│   │   └── CheckOutButton.tsx
│   │
│   ├── payments/
│   │   ├── PaymentForm.tsx      # Process payment
│   │   ├── PaymentHistory.tsx   # Transaction list
│   │   └── PaymentReceipt.tsx
│   │
│   ├── classes/
│   │   ├── ClassList.tsx        # Schedule view
│   │   ├── ClassForm.tsx        # Create/Edit class
│   │   ├── BookingForm.tsx      # Member booking
│   │   └── ClassSchedule.tsx    # Calendar view
│   │
│   ├── inventory/
│   │   ├── EquipmentList.tsx
│   │   ├── EquipmentForm.tsx
│   │   ├── MaintenanceLog.tsx
│   │   └── CategoryManager.tsx
│   │
│   └── dashboard/
│       ├── DashboardStats.tsx   # KPI cards
│       ├── RecentActivity.tsx
│       └── Charts.tsx
│
├── pages/
│   ├── LoginPage.tsx            # Staff login
│   ├── DashboardPage.tsx        # Main dashboard
│   ├── MembersPage.tsx
│   ├── AttendancePage.tsx
│   ├── PaymentsPage.tsx
│   ├── ClassesPage.tsx
│   ├── InventoryPage.tsx
│   ├── SettingsPage.tsx
│   └── ReceptionKiosk.tsx       # Member self-service
│
├── hooks/
│   ├── useAuth.ts               # Authentication logic
│   ├── useMembers.ts            # Member CRUD operations
│   ├── useAttendance.ts
│   ├── usePayments.ts
│   └── useClasses.ts
│
├── context/
│   └── AuthContext.tsx          # User session & permissions
│
├── types/
│   ├── user.types.ts
│   ├── member.types.ts
│   ├── attendance.types.ts
│   ├── payment.types.ts
│   ├── class.types.ts
│   └── inventory.types.ts
│
├── utils/
│   ├── formatters.ts            # Date, currency formatting
│   ├── validators.ts            # Form validation schemas
│   └── constants.ts             # Enums, status codes
│
└── App.tsx
```

---

## TypeScript Type Definitions

### Core Types

```typescript
// types/user.types.ts
export enum UserRole {
  ADMIN = "admin",
  STAFF = "staff",
  TRAINER = "trainer",
  RECEPTIONIST = "receptionist",
  MANAGER = "manager",
  ACCOUNTANT = "accountant",
}

export interface User {
  id: string;
  first_name: string;
  last_name: string;
  email: string;
  phone: string | null;
  role: UserRole;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

// types/member.types.ts
export interface Member {
  id: string;
  name: string;
  email: string | null;
  phone: string | null;
  date_of_birth: string | null;
  gender: string | null;
  address: string | null;
  emergency_contact: string | null;
  membership_plan: MembershipPlan | null;
  membership_start_date: string | null;
  membership_end_date: string | null;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface MembershipPlan {
  id: string;
  name: string;
  price: number;
  duration_days: number;
  description: string | null;
}

// types/attendance.types.ts
export interface Attendance {
  id: string;
  member: Member;
  check_in_time: string;
  check_out_time: string | null;
  duration_minutes: number | null;
  notes: string | null;
  checked_out: boolean;
  created_at: string;
  updated_at: string;
}

// types/payment.types.ts
export enum PaymentStatus {
  PENDING = "pending",
  PAID = "paid",
  FAILED = "failed",
  CANCELLED = "cancelled",
  REFUNDED = "refunded",
}

export interface Payment {
  id: string;
  member: Member;
  amount: number;
  payment_date: string;
  payment_method: string;
  transaction_id: string | null;
  status: PaymentStatus;
  notes: string | null;
  attendance_id: string | null;
  created_at: string;
  updated_at: string;
}

// types/class.types.ts
export enum BookingStatus {
  PENDING = "pending",
  CONFIRMED = "confirmed",
  CHECKED_IN = "checked_in",
  COMPLETED = "completed",
  CANCELLED = "cancelled",
  NO_SHOW = "no_show",
}

export interface GymClass {
  id: string;
  name: string;
  description: string | null;
  trainer: User | null;
  capacity: number;
  duration_minutes: number;
  day_of_week: string;
  start_time: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface ClassBooking {
  id: string;
  gym_class: GymClass;
  member: Member;
  booking_date: string;
  class_date: string;
  status: BookingStatus;
  created_at: string;
  updated_at: string;
}

// types/inventory.types.ts
export enum EquipmentStatus {
  OPERATIONAL = "operational",
  MAINTENANCE = "maintenance",
  BROKEN = "broken",
}

export interface Equipment {
  id: string;
  name: string;
  category: EquipmentCategory | null;
  brand: string | null;
  serial_number: string | null;
  purchase_date: string | null;
  status: EquipmentStatus;
  condition: string;
  location: string | null;
  last_maintenance_date: string | null;
  next_maintenance_date: string | null;
  quantity: number;
  is_available: boolean;
}

export interface EquipmentCategory {
  id: string;
  name: string;
  description: string | null;
}
```

---

## Key Features & User Flows

### 1. **Staff Login & Dashboard**

```
Flow:
1. Login page → Enter email/password → Call POST /auth/gimme-jwt/
2. Store JWT token in localStorage/sessionStorage
3. Redirect to Dashboard
4. Dashboard shows:
   - Total active members
   - Members currently checked in
   - Today's payments
   - Upcoming classes
   - Equipment needing maintenance
```

### 2. **Member Check-In Flow (Reception)**

```
Flow:
1. Search member by name/phone → GET /members/?search={query}
2. Select member from results
3. Validate membership status (active, not expired)
4. Click "Check In" → POST /attendance/check-in/
   Body: { member: member_id, notes: "" }
5. Show confirmation with check-in time
```

### 3. **Payment Processing**

```
Flow Option 1: During Check-In
1. After check-in, show "Process Payment" button
2. Payment form with:
   - Member info (pre-filled)
   - Amount (from membership plan)
   - Payment method (cash/card/mobile)
   - Transaction ID (optional)
3. Submit → POST /payments/
   Body: {
     member_id: uuid,
     payment_method: "cash",
     attendance_id: attendance_id (optional),
     transaction_id: "TXN123"
   }

Flow Option 2: Standalone Payment
1. Search member → Select member
2. Payment form (same as above but no attendance_id)
3. Use for membership renewals, late payments, etc.
```

### 4. **Class Booking Flow (Member Kiosk/Reception)**

```
Flow:
1. Show list of active classes → GET /classes/?is_active=true
2. Filter by day_of_week if needed
3. Select class → Show available slots
4. Search for member → GET /members/?search={query}
5. Select member and pick date
6. Submit booking → POST /classes/bookings/
   Body: {
     gym_class_id: uuid,
     member_id: uuid,
     class_date: "2025-01-15"
   }
7. Validate:
   - Class not full
   - Member doesn't already have booking for that date
8. Show confirmation
```

### 5. **Inventory Management**

```
Features:
- List equipment with status badges (operational/maintenance/broken)
- Filter by category, status, availability
- Equipment details page with maintenance history
- Log maintenance → POST /inventory/equipment/{id}/maintenance/
- Auto-update next maintenance date
```

---

## Critical Implementation Details

### 1. **Axios Configuration**

```typescript
// api/axios.config.ts
import axios from "axios";

const api = axios.create({
  baseURL: "http://localhost:8000", // Your FastAPI URL
  headers: {
    "Content-Type": "application/json",
  },
});

// Request interceptor - add JWT token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem("access_token");
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Response interceptor - handle 401 errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem("access_token");
      window.location.href = "/login";
    }
    return Promise.reject(error);
  }
);

export default api;
```

### 2. **Authentication Hook**

```typescript
// hooks/useAuth.ts
import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import api from "../api/axios.config";
import { User } from "../types/user.types";

export const useAuth = () => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();

  useEffect(() => {
    const token = localStorage.getItem("access_token");
    if (token) {
      fetchCurrentUser();
    } else {
      setLoading(false);
    }
  }, []);

  const fetchCurrentUser = async () => {
    try {
      const { data } = await api.get("/accounts/me/");
      setUser(data);
    } catch (error) {
      localStorage.removeItem("access_token");
    } finally {
      setLoading(false);
    }
  };

  const login = async (email: string, password: string) => {
    const { data } = await api.post("/auth/gimme-jwt/", { email, password });
    localStorage.setItem("access_token", data.access_token);
    await fetchCurrentUser();
    navigate("/dashboard");
  };

  const logout = () => {
    localStorage.removeItem("access_token");
    setUser(null);
    navigate("/login");
  };

  return { user, loading, login, logout };
};
```

### 3. **Pagination Handling**

```typescript
// All list endpoints return paginated data
interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  size: number;
  pages: number;
}

// Example usage in component
const [page, setPage] = useState(1);
const { data, isLoading } = useQuery(["members", page], () =>
  api.get(`/members/?page=${page}&size=20`)
);
```

### 4. **Date Formatting**

```typescript
// utils/formatters.ts
import { format, parseISO } from "date-fns";

export const formatDate = (dateString: string) => {
  return format(parseISO(dateString), "MMM dd, yyyy");
};

export const formatDateTime = (dateString: string) => {
  return format(parseISO(dateString), "MMM dd, yyyy hh:mm a");
};

export const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
  }).format(amount);
};
```

### 5. **Role-Based Access Control**

```typescript
// components/common/ProtectedRoute.tsx
interface ProtectedRouteProps {
  children: React.ReactNode;
  allowedRoles: UserRole[];
}

export const ProtectedRoute = ({
  children,
  allowedRoles,
}: ProtectedRouteProps) => {
  const { user, loading } = useAuth();

  if (loading) return <LoadingSpinner />;
  if (!user) return <Navigate to="/login" />;
  if (!allowedRoles.includes(user.role)) {
    return <div>Access Denied</div>;
  }

  return <>{children}</>;
};

// Usage in routes
<Route
  path="/inventory"
  element={
    <ProtectedRoute allowedRoles={[UserRole.ADMIN, UserRole.MANAGER]}>
      <InventoryPage />
    </ProtectedRoute>
  }
/>;
```

---

## UI/UX Requirements

### 1. **Member Search Component** (Critical)

```typescript
// Reusable component for finding members quickly
<MemberSelector
  onSelect={(member) => handleMemberSelect(member)}
  placeholder="Search by name or phone..."
/>

// Features:
- Debounced search (300ms)
- Shows: name, phone, membership status
- Highlights expired memberships
```

### 2. **Dashboard KPI Cards**

- Active Members Today
- Total Check-ins (Today/Week/Month)
- Revenue (Today/Week/Month)
- Upcoming Classes
- Equipment Status Summary

### 3. **Attendance Board** (Real-time view)

- List of currently checked-in members
- Show duration
- Quick check-out button
- Quick payment button

### 4. **Class Schedule View**

- Weekly calendar grid
- Color-coded by class type
- Show capacity (e.g., "15/20 booked")
- Click to see bookings or book member

### 5. **Payment Receipt Modal**

- Print-friendly format
- Member details
- Amount, method, date
- Transaction ID
- Membership valid until date

---

## Form Validation Rules

### Member Form

- Name: Required, min 2 characters
- Email: Valid email format (optional)
- Phone: Required, valid phone format
- Membership plan: Required
- Start date: Required if plan selected

### Payment Form

- Member: Required
- Payment method: Required
- Amount: Auto-filled from plan, read-only
- Transaction ID: Optional

### Class Form

- Name: Required
- Capacity: Required, min 1
- Duration: Required, min 15 minutes
- Day of week: Required dropdown
- Start time: Required time picker

### Booking Form

- Class: Required
- Member: Required
- Date: Required, cannot be past date
- Validates: Class capacity not exceeded

---

## Error Handling Strategy

```typescript
// Centralized error handler
const handleApiError = (error: any) => {
  if (error.response) {
    // Server responded with error
    const { status, data } = error.response;

    if (status === 404) {
      toast.error(data.detail || "Resource not found");
    } else if (status === 400) {
      toast.error(data.detail || "Invalid request");
    } else if (status === 500) {
      toast.error("Server error. Please try again.");
    }
  } else if (error.request) {
    // Request made but no response
    toast.error("Network error. Check your connection.");
  } else {
    toast.error("An unexpected error occurred.");
  }
};
```

---

## Testing Checklist

1. **Authentication**

   - Login with valid/invalid credentials
   - Token expiration handling
   - Protected route access

2. **Member Operations**

   - Create member with/without plan
   - Search functionality
   - Update member info
   - Delete member (check cascade)

3. **Check-in Flow**

   - Prevent duplicate check-ins
   - Check membership expiry
   - Check-out duration calculation

4. **Payment Processing**

   - Payment with attendance
   - Payment without attendance
   - Validate member exists
   - Transaction ID handling

5. **Class Booking**

   - Capacity validation
   - Duplicate booking prevention
   - Cancel booking
   - No-show handling

6. **Edge Cases**
   - Expired memberships
   - Full class bookings
   - Network errors
   - Concurrent operations

---

## Routes Configuration

```typescript
// App.tsx or routes.tsx
import { BrowserRouter, Routes, Route, Navigate } from "react-router-dom";

const AppRoutes = () => {
  return (
    <BrowserRouter>
      <Routes>
        {/* Public routes */}
        <Route path="/login" element={<LoginPage />} />

        {/* Protected routes */}
        <Route path="/" element={<Navigate to="/dashboard" replace />} />

        <Route
          path="/dashboard"
          element={
            <ProtectedRoute allowedRoles={Object.values(UserRole)}>
              <DashboardPage />
            </ProtectedRoute>
          }
        />

        <Route
          path="/members"
          element={
            <ProtectedRoute allowedRoles={Object.values(UserRole)}>
              <MembersPage />
            </ProtectedRoute>
          }
        />

        <Route
          path="/attendance"
          element={
            <ProtectedRoute
              allowedRoles={[
                UserRole.RECEPTIONIST,
                UserRole.ADMIN,
                UserRole.MANAGER,
              ]}
            >
              <AttendancePage />
            </ProtectedRoute>
          }
        />

        <Route
          path="/payments"
          element={
            <ProtectedRoute
              allowedRoles={[
                UserRole.RECEPTIONIST,
                UserRole.ACCOUNTANT,
                UserRole.ADMIN,
                UserRole.MANAGER,
              ]}
            >
              <PaymentsPage />
            </ProtectedRoute>
          }
        />

        <Route
          path="/classes"
          element={
            <ProtectedRoute
              allowedRoles={[
                UserRole.TRAINER,
                UserRole.ADMIN,
                UserRole.MANAGER,
              ]}
            >
              <ClassesPage />
            </ProtectedRoute>
          }
        />

        <Route
          path="/inventory"
          element={
            <ProtectedRoute allowedRoles={[UserRole.ADMIN, UserRole.MANAGER]}>
              <InventoryPage />
            </ProtectedRoute>
          }
        />

        <Route
          path="/settings"
          element={
            <ProtectedRoute allowedRoles={[UserRole.ADMIN]}>
              <SettingsPage />
            </ProtectedRoute>
          }
        />

        {/* Kiosk mode - no auth required */}
        <Route path="/kiosk" element={<ReceptionKiosk />} />
      </Routes>
    </BrowserRouter>
  );
};
```

---

This specification provides everything needed to build a complete React frontend for the gym management system.
