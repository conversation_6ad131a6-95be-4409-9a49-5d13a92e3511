import {
  Attendance,
  AttendanceRequest,
  BaseResponse,
  FeeStatusRequest,
  Member,
  MemberRequest,
} from "@/types/api";

const apiUrl = import.meta.env.VITE_API_BASE_URL;

const getAllMembers = async (): Promise<Member[]> => {
  return new Promise((resolve, reject) => {
    try {
      fetch(`${apiUrl}/members/`)
        .then((response) => response.json())
        .then((data) => {
          resolve(data);
        });
    } catch (err) {
      console.error(err);
      reject(err);
    }
  });
};

const addMember = async (member: MemberRequest): Promise<BaseResponse> => {
  return new Promise((resolve, reject) => {
    try {
      fetch(`${apiUrl}/members/`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(member),
      })
        .then((response) => response.json())
        .then((data) => {
          resolve(data as BaseResponse);
        });
    } catch (err) {
      console.error(err);
      reject(err);
    }
  });
};

const updateFeeStatus = async (
  feeStatus: FeeStatusRequest,
): Promise<BaseResponse> => {
  return new Promise((resolve, reject) => {
    try {
      fetch(`${apiUrl}/members/fee-status/`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(feeStatus),
      })
        .then((response) => response.json())
        .then((data) => {
          resolve(data as BaseResponse);
        });
    } catch (err) {
      console.error(err);
      reject(err);
    }
  });
};

const recordAttendance = async (
  attendance: AttendanceRequest,
): Promise<BaseResponse> => {
  return new Promise((resolve, reject) => {
    try {
      fetch(`${apiUrl}/attendance/`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(attendance),
      })
        .then((response) => response.json())
        .then((data) => {
          resolve(data as BaseResponse);
        });
    } catch (err) {
      console.error(err);
      reject(err);
    }
  });
};

const getAttendance = async (): Promise<Attendance[]> => {
  return new Promise((resolve, reject) => {
    try {
      fetch(`${apiUrl}/attendance/`)
        .then((response) => response.json())
        .then((data) => {
          resolve(data);
        });
    } catch (err) {
      console.error(err);
      reject(err);
    }
  });
};

const deleteAll = async (): Promise<BaseResponse> => {
  return new Promise((resolve, reject) => {
    try {
      fetch(`${apiUrl}/delete_all/`, {
        method: "DELETE",
      })
        .then((response) => response.json())
        .then((data) => {
          resolve(data as BaseResponse);
        });
    } catch (err) {
      console.error(err);
      reject(err);
    }
  });
};

export {
  getAllMembers,
  addMember,
  updateFeeStatus,
  recordAttendance,
  getAttendance,
  deleteAll,
};
