import React, { useState, useEffect } from "react";
import { inventoryApi } from "../../api/inventory.api";
import { authApi } from "../../api/auth.api";
import { Equipment, EquipmentCategory, EquipmentStatus, User } from "../../types/api";
import LoadingSpinner from "../../components/common/LoadingSpinner";
import SearchableSelect, { Option } from "../../components/common/SearchableSelect";

const InventoryPage: React.FC = () => {
  const [equipment, setEquipment] = useState<Equipment[]>([]);
  const [categories, setCategories] = useState<EquipmentCategory[]>([]);
  const [users, setUsers] = useState<User[]>([]);
  const [maintenanceLogs, setMaintenanceLogs] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedEquipment, setSelectedEquipment] = useState<Equipment | null>(null);
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showMaintenanceModal, setShowMaintenanceModal] = useState(false);
  const [showMaintenanceLogsModal, setShowMaintenanceLogsModal] = useState(false);
  const [showGeneralMaintenanceModal, setShowGeneralMaintenanceModal] = useState(false);
  const [actionLoading, setActionLoading] = useState(false);

  // Filters
  const [statusFilter, setStatusFilter] = useState<string>("");
  const [categoryFilter, setCategoryFilter] = useState<string>("");
  const [availabilityFilter, setAvailabilityFilter] = useState<string>("");

  // Form state
  const [formData, setFormData] = useState({
    name: "",
    category_id: "",
    brand: "",
    serial_number: "",
    purchase_date: "",
    condition: "excellent",
    location: "",
    quantity: 1,
  });

  const [maintenanceData, setMaintenanceData] = useState({
    description: "",
    cost: "",
    performed_by: "",
    next_maintenance_date: "",
    maintenance_type: "",
  });

  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  useEffect(() => {
    fetchData();
  }, [currentPage, statusFilter, categoryFilter, availabilityFilter]);

  const fetchData = async () => {
    try {
      setLoading(true);
      setError(null);

      const [equipmentResponse, categoriesResponse, usersResponse] = await Promise.all([
        inventoryApi.getEquipment({
          page: currentPage,
          size: 20,
          status: statusFilter || undefined,
          category_id: categoryFilter || undefined,
          is_available: availabilityFilter ? availabilityFilter === "true" : undefined,
        }),
        inventoryApi.getCategories(),
        authApi.getAllUsers(),
      ]);

      setEquipment(equipmentResponse.items);
      setTotalPages(equipmentResponse.pages);
      setCategories(categoriesResponse.items || categoriesResponse);
      setUsers(usersResponse.items);
    } catch (err) {
      console.error("Error fetching data:", err);
      setError("Failed to load inventory data");
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    setFormData({
      name: "",
      category_id: "",
      brand: "",
      serial_number: "",
      purchase_date: "",
      condition: "excellent",
      location: "",
      quantity: 1,
    });
  };

  const resetMaintenanceForm = () => {
    setMaintenanceData({
      description: "",
      cost: "",
      performed_by: "",
      next_maintenance_date: "",
      maintenance_type: "",
    });
  };

  // Convert categories to options for SearchableSelect
  const categoryOptions: Option[] = [
    { value: "", label: "All Categories" },
    ...categories.map(category => ({
      value: category.id,
      label: category.name
    }))
  ];

  const statusOptions: Option[] = [
    { value: "", label: "All Statuses" },
    { value: "operational", label: "Operational" },
    { value: "maintenance", label: "Maintenance" },
    { value: "broken", label: "Broken" }
  ];

  const availabilityOptions: Option[] = [
    { value: "", label: "All" },
    { value: "true", label: "Available" },
    { value: "false", label: "Not Available" }
  ];

  const userOptions: Option[] = users.map(user => ({
    value: user.id,
    label: `${user.first_name} ${user.last_name} (${user.role})`
  }));

  const handleAddEquipment = () => {
    resetForm();
    setShowAddModal(true);
  };

  const handleEditEquipment = (item: Equipment) => {
    setSelectedEquipment(item);
    setFormData({
      name: item.name,
      category_id: item.category?.id || "",
      brand: item.brand || "",
      serial_number: item.serial_number || "",
      purchase_date: item.purchase_date || "",
      condition: item.condition,
      location: item.location || "",
      quantity: item.quantity,
    });
    setShowEditModal(true);
  };

  const handleDeleteEquipment = (item: Equipment) => {
    setSelectedEquipment(item);
    setShowDeleteModal(true);
  };

  const handleMaintenanceLog = (item: Equipment) => {
    setSelectedEquipment(item);
    resetMaintenanceForm();
    setShowMaintenanceModal(true);
  };

  const handleViewMaintenanceLogs = async (item: Equipment) => {
    try {
      setSelectedEquipment(item);
      setActionLoading(true);
      const logs = await inventoryApi.getMaintenanceHistory(item.id);
      setMaintenanceLogs(Array.isArray(logs) ? logs : (logs as any).items || []);
      setShowMaintenanceLogsModal(true);
    } catch (err) {
      console.error("Error fetching maintenance logs:", err);
      setError("Failed to load maintenance logs");
    } finally {
      setActionLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'number' ? parseInt(value) || 0 : value
    }));
  };

  const handleMaintenanceInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setMaintenanceData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmitAdd = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name || formData.quantity <= 0) {
      setError("Please fill in all required fields with valid values");
      return;
    }

    try {
      setActionLoading(true);
      setError(null);

      await inventoryApi.createEquipment(formData);

      setShowAddModal(false);
      resetForm();
      fetchData();
    } catch (err: any) {
      console.error("Error creating equipment:", err);
      setError(err.response?.data?.detail || "Failed to create equipment");
    } finally {
      setActionLoading(false);
    }
  };

  const handleSubmitEdit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!selectedEquipment) return;

    if (!formData.name || formData.quantity <= 0) {
      setError("Please fill in all required fields with valid values");
      return;
    }

    try {
      setActionLoading(true);
      setError(null);

      await inventoryApi.updateEquipment(selectedEquipment.id, formData);

      setShowEditModal(false);
      setSelectedEquipment(null);
      resetForm();
      fetchData();
    } catch (err: any) {
      console.error("Error updating equipment:", err);
      setError(err.response?.data?.detail || "Failed to update equipment");
    } finally {
      setActionLoading(false);
    }
  };

  const confirmDeleteEquipment = async () => {
    if (!selectedEquipment) return;

    try {
      setActionLoading(true);
      await inventoryApi.deleteEquipment(selectedEquipment.id);
      setShowDeleteModal(false);
      setSelectedEquipment(null);
      fetchData();
    } catch (err: any) {
      console.error("Delete equipment failed:", err);
      setError(err.response?.data?.detail || "Failed to delete equipment");
    } finally {
      setActionLoading(false);
    }
  };

  const handleSubmitMaintenance = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!selectedEquipment || !maintenanceData.description || !maintenanceData.maintenance_type) {
      setError("Please fill in all required fields");
      return;
    }

    try {
      setActionLoading(true);
      setError(null);

      const maintenancePayload = {
        maintenance_type: maintenanceData.maintenance_type,
        description: maintenanceData.description,
        cost: maintenanceData.cost ? parseFloat(maintenanceData.cost) : undefined,
        performed_by: maintenanceData.performed_by ? parseInt(maintenanceData.performed_by) : undefined,
        next_maintenance_date: maintenanceData.next_maintenance_date || undefined,
      };

      await inventoryApi.logMaintenance(maintenancePayload, selectedEquipment.id);

      setShowMaintenanceModal(false);
      setSelectedEquipment(null);
      resetMaintenanceForm();
      fetchData();
    } catch (err: any) {
      console.error("Error logging maintenance:", err);
      setError(err.response?.data?.detail || "Failed to log maintenance");
    } finally {
      setActionLoading(false);
    }
  };

  const getStatusBadge = (status: EquipmentStatus) => {
    const colors = {
      [EquipmentStatus.OPERATIONAL]: "bg-green-100 text-green-800",
      [EquipmentStatus.MAINTENANCE]: "bg-yellow-100 text-yellow-800",
      [EquipmentStatus.BROKEN]: "bg-red-100 text-red-800",
    };

    return (
      <span className={`px-2 py-1 text-xs font-medium rounded-full ${colors[status]}`}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </span>
    );
  };

  const getConditionBadge = (condition: string) => {
    const colors = {
      excellent: "bg-green-100 text-green-800",
      good: "bg-blue-100 text-blue-800",
      fair: "bg-yellow-100 text-yellow-800",
      poor: "bg-red-100 text-red-800",
    };

    return (
      <span className={`px-2 py-1 text-xs font-medium rounded-full ${colors[condition as keyof typeof colors] || 'bg-gray-100 text-gray-800'}`}>
        {condition.charAt(0).toUpperCase() + condition.slice(1)}
      </span>
    );
  };

  return (
    <div className="p-6">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900">Equipment Inventory</h1>
        <p className="text-gray-600 mt-2">Manage gym equipment and track maintenance</p>
      </div>

      {/* Filters */}
      <div className="bg-white p-4 rounded-lg shadow mb-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Status</label>
            <SearchableSelect
              options={statusOptions}
              value={statusFilter}
              onChange={setStatusFilter}
              placeholder="All Statuses"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Category</label>
            <SearchableSelect
              options={categoryOptions}
              value={categoryFilter}
              onChange={setCategoryFilter}
              placeholder="All Categories"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Availability</label>
            <SearchableSelect
              options={availabilityOptions}
              value={availabilityFilter}
              onChange={setAvailabilityFilter}
              placeholder="All"
            />
          </div>

          <div className="flex items-end">
            <button
              onClick={() => {
                setStatusFilter("");
                setCategoryFilter("");
                setAvailabilityFilter("");
                setCurrentPage(1);
              }}
              className="w-full bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600"
            >
              Clear Filters
            </button>
          </div>
        </div>
      </div>

      {/* Equipment Table */}
      <div className="bg-white rounded-lg shadow overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex justify-between items-center">
            <h2 className="text-lg font-semibold text-gray-900">
              Equipment ({equipment.length})
            </h2>
            <div className="flex space-x-2">
              <button
                onClick={() => setShowGeneralMaintenanceModal(true)}
                className="bg-purple-600 text-white px-4 py-2 rounded hover:bg-purple-700"
              >
                General Maintenance
              </button>
              <button
                onClick={handleAddEquipment}
                className="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700"
              >
                Add Equipment
              </button>
            </div>
          </div>
        </div>

        {loading ? (
          <div className="p-8 text-center">
            <LoadingSpinner size="lg" />
          </div>
        ) : error ? (
          <div className="p-8 text-center">
            <div className="bg-red-50 border border-red-200 p-4 rounded-lg">
              <p className="text-red-800">{error}</p>
              <button
                onClick={fetchData}
                className="mt-2 bg-red-600 text-white px-4 py-2 rounded text-sm hover:bg-red-700"
              >
                Retry
              </button>
            </div>
          </div>
        ) : equipment.length === 0 ? (
          <div className="p-8 text-center text-gray-500">
            No equipment found. Add your first equipment item to get started.
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Equipment
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Category
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Condition
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Quantity
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Location
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {equipment.map((item) => (
                  <tr key={item.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900">
                          {item.name}
                        </div>
                        {item.brand && (
                          <div className="text-sm text-gray-500">
                            {item.brand}
                          </div>
                        )}
                        {item.serial_number && (
                          <div className="text-xs text-gray-400">
                            S/N: {item.serial_number}
                          </div>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {item.category?.name || "No category"}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {getStatusBadge(item.status)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {getConditionBadge(item.condition)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {item.quantity}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {item.location || "Not specified"}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex space-x-1">
                        <button
                          onClick={() => handleEditEquipment(item)}
                          className="text-blue-600 hover:text-blue-900 px-2 py-1 rounded hover:bg-blue-50 text-xs"
                        >
                          Edit
                        </button>
                        <button
                          onClick={() => handleMaintenanceLog(item)}
                          className="text-green-600 hover:text-green-900 px-2 py-1 rounded hover:bg-green-50 text-xs"
                        >
                          Maintenance
                        </button>
                        <button
                          onClick={() => handleViewMaintenanceLogs(item)}
                          className="text-purple-600 hover:text-purple-900 px-2 py-1 rounded hover:bg-purple-50 text-xs"
                        >
                          Logs
                        </button>
                        <button
                          onClick={() => handleDeleteEquipment(item)}
                          className="text-red-600 hover:text-red-900 px-2 py-1 rounded hover:bg-red-50 text-xs"
                        >
                          Delete
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="px-6 py-4 border-t border-gray-200">
            <div className="flex justify-between items-center">
              <div className="text-sm text-gray-700">
                Page {currentPage} of {totalPages}
              </div>
              <div className="flex space-x-2">
                <button
                  onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                  disabled={currentPage === 1}
                  className="px-3 py-1 border border-gray-300  text-gray-700 rounded text-sm disabled:bg-gray-100 disabled:cursor-not-allowed hover:bg-gray-50"
                >
                  Previous
                </button>
                <button
                  onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                  disabled={currentPage === totalPages}
                  className="px-3 py-1 border border-gray-300  text-gray-700 rounded text-sm disabled:bg-gray-100 disabled:cursor-not-allowed hover:bg-gray-50"
                >
                  Next
                </button>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Add Equipment Modal */}
      {showAddModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg max-w-md w-full mx-4 max-h-screen overflow-y-auto">
            <h3 className="text-lg font-semibold mb-4 text-gray-700">Add New Equipment</h3>

            {error && (
              <div className="bg-red-50 border border-red-200 p-4 rounded-lg mb-4">
                <p className="text-red-800 text-sm">{error}</p>
              </div>
            )}

            <form onSubmit={handleSubmitAdd} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Equipment Name *
                </label>
                <input
                  type="text"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  required
                  className="w-full p-3  text-gray-700 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="e.g., Treadmill"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Category
                </label>
                <select
                  name="category_id"
                  value={formData.category_id}
                  onChange={handleInputChange}
                  className="w-full p-3 border text-gray-700  border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">Select category</option>
                  {categories.map((category) => (
                    <option key={category.id} value={category.id}>
                      {category.name}
                    </option>
                  ))}
                </select>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Brand
                  </label>
                  <input
                    type="text"
                    name="brand"
                    value={formData.brand}
                    onChange={handleInputChange}
                    className="w-full p-3 border text-gray-700 border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="e.g., NordicTrack"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Serial Number
                  </label>
                  <input
                    type="text"
                    name="serial_number"
                    value={formData.serial_number}
                    onChange={handleInputChange}
                    className="w-full p-3 border text-gray-700 border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="S/N"
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Purchase Date
                  </label>
                  <input
                    type="date"
                    name="purchase_date"
                    value={formData.purchase_date}
                    onChange={handleInputChange}
                    className="w-full p-3 border text-gray-700 border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Condition *
                  </label>
                  <select
                    name="condition"
                    value={formData.condition}
                    onChange={handleInputChange}
                    required
                    className="w-full p-3 border text-gray-700 border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="excellent">Excellent</option>
                    <option value="good">Good</option>
                    <option value="fair">Fair</option>
                    <option value="poor">Poor</option>
                  </select>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Location
                  </label>
                  <input
                    type="text"
                    name="location"
                    value={formData.location}
                    onChange={handleInputChange}
                    className="w-full p-3 border text-gray-700 border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="e.g., Main Floor"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Quantity *
                  </label>
                  <input
                    type="number"
                    name="quantity"
                    value={formData.quantity}
                    onChange={handleInputChange}
                    required
                    min="1"
                    className="w-full p-3 border text-gray-700 border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              </div>

              <div className="flex justify-end space-x-3 pt-4">
                <button
                  type="button"
                  onClick={() => {
                    setShowAddModal(false);
                    resetForm();
                    setError(null);
                  }}
                  disabled={actionLoading}
                  className="px-4 py-2 bg-gray-300 text-gray-700 rounded hover:bg-gray-400 disabled:opacity-50"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={actionLoading}
                  className="px-4 py-2 bg-blue-600 text-gray-700 text-white rounded hover:bg-blue-700 disabled:opacity-50 flex items-center"
                >
                  {actionLoading ? (
                    <>
                      <LoadingSpinner size="sm" className="mr-2" />
                      Adding...
                    </>
                  ) : (
                    "Add Equipment"
                  )}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Edit Equipment Modal */}
      {showEditModal && selectedEquipment && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg max-w-md w-full mx-4 max-h-screen overflow-y-auto">
            <h3 className="text-lg font-semibold mb-4 text-gray-700">Edit Equipment</h3>

            {error && (
              <div className="bg-red-50 border border-red-200 p-4 rounded-lg mb-4">
                <p className="text-red-800 text-sm">{error}</p>
              </div>
            )}

            <form onSubmit={handleSubmitEdit} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Equipment Name *
                </label>
                <input
                  type="text"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  required
                  className="w-full p-3 border text-gray-700 border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Category
                </label>
                <select
                  name="category_id"
                  value={formData.category_id}
                  onChange={handleInputChange}
                  className="w-full p-3 border text-gray-700 border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">Select category</option>
                  {categories.map((category) => (
                    <option key={category.id} value={category.id}>
                      {category.name}
                    </option>
                  ))}
                </select>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Brand
                  </label>
                  <input
                    type="text"
                    name="brand"
                    value={formData.brand}
                    onChange={handleInputChange}
                    className="w-full p-3 border text-gray-700 border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Serial Number
                  </label>
                  <input
                    type="text"
                    name="serial_number"
                    value={formData.serial_number}
                    onChange={handleInputChange}
                    className="w-full p-3 border text-gray-700 border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Purchase Date
                  </label>
                  <input
                    type="date"
                    name="purchase_date"
                    value={formData.purchase_date}
                    onChange={handleInputChange}
                    className="w-full p-3 border  text-gray-700 border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Condition *
                  </label>
                  <select
                    name="condition"
                    value={formData.condition}
                    onChange={handleInputChange}
                    required
                    className="w-full p-3 border text-gray-700 border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="excellent">Excellent</option>
                    <option value="good">Good</option>
                    <option value="fair">Fair</option>
                    <option value="poor">Poor</option>
                  </select>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Location
                  </label>
                  <input
                    type="text"
                    name="location"
                    value={formData.location}
                    onChange={handleInputChange}
                    className="w-full p-3 border  text-gray-700 border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Quantity *
                  </label>
                  <input
                    type="number"
                    name="quantity"
                    value={formData.quantity}
                    onChange={handleInputChange}
                    required
                    min="1"
                    className="w-full p-3 border text-gray-700 border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              </div>

              <div className="flex justify-end space-x-3 pt-4">
                <button
                  type="button"
                  onClick={() => {
                    setShowEditModal(false);
                    setSelectedEquipment(null);
                    resetForm();
                    setError(null);
                  }}
                  disabled={actionLoading}
                  className="px-4 py-2 bg-gray-300 text-gray-700 rounded hover:bg-gray-400 disabled:opacity-50"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={actionLoading}
                  className="px-4 py-2 bg-blue-600  text-gray-700text-white rounded hover:bg-blue-700 disabled:opacity-50 flex items-center"
                >
                  {actionLoading ? (
                    <>
                      <LoadingSpinner size="sm" className="mr-2" />
                      Updating...
                    </>
                  ) : (
                    "Update Equipment"
                  )}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Delete Confirmation Modal */}
      {showDeleteModal && selectedEquipment && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg max-w-md w-full mx-4">
            <h3 className="text-lg font-semibold mb-4">Confirm Delete</h3>
            <p className="text-gray-600 mb-6">
              Are you sure you want to delete <strong>{selectedEquipment.name}</strong>?
              This action cannot be undone.
            </p>
            <div className="flex justify-end space-x-3">
              <button
                onClick={() => {
                  setShowDeleteModal(false);
                  setSelectedEquipment(null);
                  setError(null);
                }}
                disabled={actionLoading}
                className="px-4 py-2 bg-gray-300 text-gray-700 rounded hover:bg-gray-400 disabled:opacity-50"
              >
                Cancel
              </button>
              <button
                onClick={confirmDeleteEquipment}
                disabled={actionLoading}
                className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 disabled:opacity-50 flex items-center"
              >
                {actionLoading ? (
                  <>
                    <LoadingSpinner size="sm" className="mr-2" />
                    Deleting...
                  </>
                ) : (
                  "Delete"
                )}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Maintenance Log Modal */}
      {showMaintenanceModal && selectedEquipment && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg max-w-md w-full mx-4">
            <h3 className="text-lg font-semibold mb-4">Log Maintenance for {selectedEquipment.name}</h3>

            {error && (
              <div className="bg-red-50 border border-red-200 p-4 rounded-lg mb-4">
                <p className="text-red-800 text-sm">{error}</p>
              </div>
            )}

            <form onSubmit={handleSubmitMaintenance} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Maintenance Description *
                </label>
                <textarea
                  name="description"
                  value={maintenanceData.description}
                  onChange={handleMaintenanceInputChange}
                  required
                  rows={3}
                  className="w-full p-3  text-gray-700 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Describe the maintenance performed..."
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Cost ($)
                  </label>
                  <input
                    type="number"
                    name="cost"
                    value={maintenanceData.cost}
                    onChange={handleMaintenanceInputChange}
                    min="0"
                    step="0.01"
                    className="w-full p-3  text-gray-700 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="0.00"
                  />
                </div>

                


                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Maintenance Type
                  </label>
                  <select
                    name="maintenance_type"
                    value={maintenanceData.maintenance_type}
                    onChange={handleMaintenanceInputChange}
                    className="w-full p-3 border text-gray-700 border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    required
                  >
                    <option value="">Select type</option>
                    <option value="inspection">Inspection</option>
                    <option value="cleaning">Cleaning</option>
                    <option value="lubrication">Lubrication</option>
                    <option value="repair">Repair</option>
                    <option value="replacement">Replacement</option>
                    <option value="calibration">Calibration</option>
                    <option value="software_update">Software Update</option>
                    <option value="preventive_maintenance">Preventive Maintenance</option>
                    <option value="emergency_repair">Emergency Repair</option>
                    <option value="testing">Testing</option>
                  </select>
                </div>


                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Performed By
                  </label>
                  <SearchableSelect
                    options={userOptions}
                    value={maintenanceData.performed_by}
                    onChange={(value) => setMaintenanceData(prev => ({ ...prev, performed_by: value }))}
                    placeholder="Select technician"
                    name="performed_by"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Next Maintenance Date
                </label>
                <input
                  type="date"
                  name="next_maintenance_date"
                  value={maintenanceData.next_maintenance_date}
                  onChange={handleMaintenanceInputChange}
                  className="w-full p-3 border  text-gray-700 border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              <div className="flex justify-end space-x-3 pt-4">
                <button
                  type="button"
                  onClick={() => {
                    setShowMaintenanceModal(false);
                    setSelectedEquipment(null);
                    resetMaintenanceForm();
                    setError(null);
                  }}
                  disabled={actionLoading}
                  className="px-4 py-2 bg-gray-300 text-gray-700 rounded hover:bg-gray-400 disabled:opacity-50"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={actionLoading}
                  className="px-4 py-2 bg-green-600  text-gray-700 text-white rounded hover:bg-green-700 disabled:opacity-50 flex items-center"
                >
                  {actionLoading ? (
                    <>
                      <LoadingSpinner size="sm" className="mr-2" />
                      Logging...
                    </>
                  ) : (
                    "Log Maintenance"
                  )}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Maintenance Logs Modal */}
      {showMaintenanceLogsModal && selectedEquipment && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg max-w-4xl w-full mx-4 max-h-screen overflow-y-auto">
            <h3 className="text-lg font-semibold mb-4">
              Maintenance History for {selectedEquipment.name}
            </h3>

            <div className="max-h-96 overflow-y-auto">
              {maintenanceLogs.length > 0 ? (
                <div className="space-y-4">
                  {maintenanceLogs.map((log: any, index: number) => (
                    <div key={index} className="border border-gray-200 rounded-lg p-4">
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <p className="text-sm font-medium text-gray-700">Description:</p>
                          <p className="text-sm text-gray-900">{log.description}</p>
                        </div>
                        <div>
                          <p className="text-sm font-medium text-gray-700">Date:</p>
                          <p className="text-sm text-gray-900">
                            {new Date(log.created_at).toLocaleDateString()}
                          </p>
                        </div>
                        {log.cost && (
                          <div>
                            <p className="text-sm font-medium text-gray-700">Cost:</p>
                            <p className="text-sm text-gray-900">${log.cost}</p>
                          </div>
                        )}
                        {log.performed_by && (
                          <div>
                            <p className="text-sm font-medium text-gray-700">Performed By:</p>
                            <p className="text-sm text-gray-900">{log.performed_by}</p>
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-gray-500 text-center py-8">No maintenance logs found</p>
              )}
            </div>

            <div className="flex justify-end pt-4">
              <button
                onClick={() => {
                  setShowMaintenanceLogsModal(false);
                  setSelectedEquipment(null);
                  setMaintenanceLogs([]);
                }}
                className="px-4 py-2 bg-gray-300 text-gray-700 rounded hover:bg-gray-400"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}

      {/* General Maintenance Modal */}
      {showGeneralMaintenanceModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg max-w-md w-full mx-4">
            <h3 className="text-lg font-semibold mb-4">Log General Maintenance</h3>

            {error && (
              <div className="bg-red-50 border border-red-200 p-4 rounded-lg mb-4">
                <p className="text-red-800 text-sm">{error}</p>
              </div>
            )}

            <form onSubmit={async (e) => {
              e.preventDefault();
              try {
                setActionLoading(true);
                setError(null);

                const maintenancePayload = {
                  description: maintenanceData.description,
                  cost: maintenanceData.cost ? parseFloat(maintenanceData.cost) : undefined,
                  performed_by: maintenanceData.performed_by ? parseInt(maintenanceData.performed_by) : undefined,
                  next_maintenance_date: maintenanceData.next_maintenance_date || undefined,
                };

                await inventoryApi.logMaintenance(maintenancePayload);

                setShowGeneralMaintenanceModal(false);
                resetMaintenanceForm();
                fetchData();
              } catch (err: any) {
                console.error("Error logging general maintenance:", err);
                setError(err.response?.data?.detail || "Failed to log maintenance");
              } finally {
                setActionLoading(false);
              }
            }} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Maintenance Description *
                </label>
                <textarea
                  name="description"
                  value={maintenanceData.description}
                  onChange={handleMaintenanceInputChange}
                  required
                  rows={3}
                  className="w-full p-3 border text-gray-700 border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Describe the general maintenance performed..."
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Cost ($)
                  </label>
                  <input
                    type="number"
                    name="cost"
                    value={maintenanceData.cost}
                    onChange={handleMaintenanceInputChange}
                    min="0"
                    step="0.01"
                    className="w-full p-3 border text-gray-700 border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="0.00"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Performed By
                  </label>
                  <SearchableSelect
                    options={userOptions}
                    value={maintenanceData.performed_by}
                    onChange={(value) => setMaintenanceData(prev => ({ ...prev, performed_by: value }))}
                    placeholder="Select technician"
                    name="performed_by"
                  />
                </div>
              </div>

              <div className="flex justify-end space-x-3 pt-4">
                <button
                  type="button"
                  onClick={() => {
                    setShowGeneralMaintenanceModal(false);
                    resetMaintenanceForm();
                    setError(null);
                  }}
                  disabled={actionLoading}
                  className="px-4 py-2 bg-gray-300 text-gray-700 rounded hover:bg-gray-400 disabled:opacity-50"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={actionLoading}
                  className="px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700 disabled:opacity-50 flex items-center"
                >
                  {actionLoading ? (
                    <>
                      <LoadingSpinner size="sm" className="mr-2" />
                      Logging...
                    </>
                  ) : (
                    "Log Maintenance"
                  )}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default InventoryPage;
